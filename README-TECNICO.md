# Nord Investimentos - Sistema de Relatórios Automatizados

## 📋 Visão Geral

Sistema desenvolvido para automatizar a geração de relatórios financeiros para consultores da Nord Investimentos, reduzindo significativamente o tempo gasto na elaboração de relatórios para clientes (50-100 clientes por consultor).

## 🏗️ Arquitetura do Sistema

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Integrações   │
│   (Next.js)     │◄──►│   (Node.js)     │◄──►│  Open Finance   │
│                 │    │                 │    │      n8n        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Interface     │    │   PostgreSQL    │    │   Relatórios    │
│   do Usuário    │    │   Database      │    │ (PDF/Word/etc)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🛠️ Stack Tecnológica

### Frontend
- **Next.js 14** - Framework React com SSR/SSG
- **TypeScript** - Tipagem estática para maior robustez
- **Tailwind CSS** - Framework CSS utilitário
- **React Hook Form** - Gerenciamento de formulários
- **Zustand** - Gerenciamento de estado global
- **React Query** - Cache e sincronização de dados

### Backend
- **Node.js** - Runtime JavaScript
- **Express.js** - Framework web minimalista
- **TypeScript** - Tipagem estática
- **Prisma** - ORM moderno para PostgreSQL
- **JWT** - Autenticação e autorização
- **Zod** - Validação de schemas

### Banco de Dados
- **PostgreSQL** - Banco relacional robusto
- **Redis** - Cache para dados do Open Finance

### Integrações
- **Open Finance APIs** - Extração de dados bancários
- **n8n Webhooks** - Automação de workflows
- **Puppeteer** - Geração de PDFs
- **docx** - Geração de documentos Word
- **ExcelJS** - Geração de planilhas

## 📁 Estrutura do Projeto

```
nord-investimentos-relatorios/
├── frontend/                 # Aplicação Next.js
│   ├── src/
│   │   ├── app/             # App Router (Next.js 13+)
│   │   ├── components/      # Componentes reutilizáveis
│   │   ├── hooks/           # Custom hooks
│   │   ├── lib/             # Utilitários e configurações
│   │   ├── store/           # Gerenciamento de estado
│   │   └── types/           # Definições de tipos TypeScript
│   ├── public/              # Arquivos estáticos
│   └── package.json
├── backend/                 # API Node.js
│   ├── src/
│   │   ├── controllers/     # Controladores das rotas
│   │   ├── middleware/      # Middlewares customizados
│   │   ├── models/          # Modelos de dados (Prisma)
│   │   ├── routes/          # Definição das rotas
│   │   ├── services/        # Lógica de negócio
│   │   ├── utils/           # Funções utilitárias
│   │   └── integrations/    # Integrações externas
│   ├── prisma/              # Schema e migrações do banco
│   └── package.json
├── docs/                    # Documentação do projeto
├── docker-compose.yml       # Configuração Docker
└── package.json            # Configuração do workspace
```

## 🚀 Instalação e Configuração

### Pré-requisitos
- Node.js 18+
- PostgreSQL 14+
- Redis (opcional, para cache)
- Docker (opcional)

### Instalação
```bash
# Clonar o repositório
git clone <repository-url>
cd nord-investimentos-relatorios

# Instalar dependências de todos os projetos
npm run install:all

# Configurar variáveis de ambiente
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env

# Configurar banco de dados
cd backend
npx prisma migrate dev
npx prisma generate
```

### Desenvolvimento
```bash
# Iniciar todos os serviços em modo desenvolvimento
npm run dev

# Ou iniciar individualmente:
npm run dev:frontend  # Frontend em http://localhost:3000
npm run dev:backend   # Backend em http://localhost:3001
```

## 📊 Funcionalidades Principais

### 1. Integração Open Finance
- Conexão segura com múltiplas instituições financeiras
- Extração automática de dados de contas correntes, poupança e investimentos
- Sincronização periódica de transações

### 2. Automação com n8n
- Workflows personalizáveis para cada tipo de relatório
- Triggers automáticos baseados em eventos
- Integração via webhooks

### 3. Geração de Relatórios
- **PDF**: Relatórios executivos e detalhados
- **Word**: Documentos editáveis para personalização
- **Excel**: Planilhas com dados brutos e análises
- **Google Sheets**: Colaboração em tempo real

### 4. Métricas e Análises
- Análise de desempenho de investimentos
- Cálculo de indicadores financeiros
- Comparação com benchmarks de mercado
- Projeções e cenários

## 🔒 Segurança

- Autenticação JWT com refresh tokens
- Criptografia de dados sensíveis
- Logs de auditoria
- Rate limiting nas APIs
- Validação rigorosa de inputs

## 📈 Monitoramento

- Logs estruturados com Winston
- Métricas de performance
- Health checks automáticos
- Alertas para falhas de integração

## 🧪 Testes

```bash
# Executar todos os testes
npm run test

# Testes por módulo
npm run test:frontend
npm run test:backend
```

## 📝 Contribuição

1. Seguir os padrões de código estabelecidos
2. Escrever testes para novas funcionalidades
3. Documentar mudanças significativas
4. Usar commits semânticos

## 📞 Suporte

Para dúvidas técnicas ou suporte, consulte a documentação em `/docs` ou entre em contato com a equipe de desenvolvimento.
