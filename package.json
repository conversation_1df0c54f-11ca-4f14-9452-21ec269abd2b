{"name": "nord-investimentos-relatorios", "version": "1.0.0", "description": "Ferramenta de automação de relatórios para consultores da Nord Investimentos", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "npm run start:backend", "start:backend": "cd backend && npm run start", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && npm run test", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint"}, "keywords": ["nord-investimentos", "relatorios", "open-finance", "n8n", "automacao"], "author": "Nord Investimentos", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["frontend", "backend"]}